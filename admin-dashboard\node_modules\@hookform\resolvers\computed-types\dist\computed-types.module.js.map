{"version": 3, "file": "computed-types.module.js", "sources": ["../src/computed-types.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ValidationError } from 'computed-types';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst isValidationError = (error: any): error is ValidationError =>\n  error.errors != null;\n\nconst parseErrorSchema = (computedTypesError: ValidationError) => {\n  const parsedErrors: FieldErrors = {};\n  return (computedTypesError.errors || []).reduce((acc, error) => {\n    acc[error.path.join('.')] = {\n      type: error.error.name,\n      message: error.error.message,\n    };\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const computedTypesResolver: Resolver =\n  (schema) => async (values, _, options) => {\n    try {\n      const data = await schema(values);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {},\n        values: data,\n      };\n    } catch (error: any) {\n      if (isValidationError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(parseErrorSchema(error), options),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["computedTypesResolver", "schema", "values", "_", "options", "Promise", "resolve", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "_catch", "error", "isValidationError", "toNestErrors", "computedTypesError", "reduce", "acc", "path", "join", "type", "name", "message", "e", "reject"], "mappings": "+EAKA,IAeaA,EACX,SAACC,GAAkBC,OAAAA,SAAAA,EAAQC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,gCACnCD,QAAAC,QACiBL,EAAOC,IAAOK,KAAA,SAA3BC,GAIN,OAFAJ,EAAQK,2BAA6BC,EAAuB,CAAA,EAAIN,GAEzD,CACLO,OAAQ,CAAE,EACVT,OAAQM,EACR,4DATmCI,CAAA,EAU9BC,SAAAA,GACP,GA3BoB,SAACA,GACzB,OAAgB,MAAhBA,EAAMF,MAAc,CA0BZG,CAAkBD,GACpB,MAAO,CACLX,OAAQ,CAAA,EACRS,OAAQI,GA3BQC,EA2BsBH,GAzBtCG,EAAmBL,QAAU,IAAIM,OAAO,SAACC,EAAKL,GAMpD,OALAK,EAAIL,EAAMM,KAAKC,KAAK,MAAQ,CAC1BC,KAAMR,EAAMA,MAAMS,KAClBC,QAASV,EAAMA,MAAMU,SAGhBL,CACT,EARkC,CAAA,IA0BoBd,IA3B/B,IAACY,EA+BpB,MAAMH,CACR,GACF,CAAC,MAAAW,GAAAnB,OAAAA,QAAAoB,OAAAD,EAAA,CAAA,CAAA"}