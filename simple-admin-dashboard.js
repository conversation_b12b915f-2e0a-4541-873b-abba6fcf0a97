const http = require('http');
const url = require('url');

const PORT = 3001;

// Simple HTTP server for Admin Dashboard
const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  if (path === '/health') {
    res.setHeader('Content-Type', 'application/json');
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'OK',
      service: 'PromoTun Admin Dashboard',
      timestamp: new Date().toISOString(),
      port: PORT
    }));
  } else {
    res.setHeader('Content-Type', 'text/html');
    res.writeHead(200);
    res.end(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromoTun Admin Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .status {
            background: rgba(40, 167, 69, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }
        .api-info {
            background: rgba(23, 162, 184, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        .success { color: #28a745; font-weight: bold; }
        a { color: #ffc107; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .timestamp { font-size: 0.9em; opacity: 0.8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>👑 PromoTun Admin Dashboard</h1>
        
        <div class="status">
            <h2 class="success">✅ Admin Dashboard is Running!</h2>
            <p>The PromoTun Admin Dashboard is successfully running on port ${PORT}.</p>
            <p class="timestamp">Started: ${new Date().toISOString()}</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>👥 User Management</h3>
                <p>Manage users, merchants, and administrators across the platform.</p>
            </div>
            <div class="feature">
                <h3>🏪 Merchant Approval</h3>
                <p>Review and approve merchant applications and business verifications.</p>
            </div>
            <div class="feature">
                <h3>📊 System Analytics</h3>
                <p>Monitor platform-wide metrics, usage statistics, and performance.</p>
            </div>
            <div class="feature">
                <h3>🛡️ Security</h3>
                <p>Monitor security events, manage access controls, and audit logs.</p>
            </div>
            <div class="feature">
                <h3>⚙️ System Settings</h3>
                <p>Configure platform settings, features, and operational parameters.</p>
            </div>
            <div class="feature">
                <h3>🔍 Content Moderation</h3>
                <p>Review and moderate user-generated content and promotions.</p>
            </div>
        </div>

        <div class="api-info">
            <h3>🔗 API Integration</h3>
            <p><strong>Backend API:</strong> <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></p>
            <p><strong>Health Check:</strong> <a href="/health">/health</a></p>
            <p><strong>Categories API:</strong> <a href="http://localhost:5000/api/categories" target="_blank">http://localhost:5000/api/categories</a></p>
        </div>

        <div class="api-info">
            <h3>🌐 Other Services</h3>
            <p><strong>Merchant Portal:</strong> <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></p>
            <p><strong>Backend API:</strong> <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></p>
            <p><strong>Main Gateway:</strong> <a href="http://localhost" target="_blank">http://localhost</a></p>
        </div>

        <div style="text-align: center; margin-top: 40px; opacity: 0.8;">
            <p>PromoTun Admin Dashboard - Platform Administration</p>
            <p>🛡️ Secure platform management and oversight!</p>
        </div>
    </div>
</body>
</html>
    `);
  }
});

server.listen(PORT, () => {
  console.log(`👑 PromoTun Admin Dashboard running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard: http://localhost:${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`✅ Ready for platform administration!`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Admin Dashboard...');
  server.close(() => {
    console.log('✅ Admin Dashboard closed');
    process.exit(0);
  });
});
