var e=require("@hookform/resolvers"),r=require("react-hook-form"),s=require("valibot");exports.valibotResolver=function(t,o,a){return void 0===a&&(a={}),function(i,u,n){try{var v=!n.shouldUseNativeValidation&&"all"===n.criteriaMode;return Promise.resolve(s.safeParseAsync(t,i,Object.assign({},o,{abortPipeEarly:!v}))).then(function(t){if(t.issues){for(var o={};t.issues.length;){var u=t.issues[0],l=s.getDotPath(u);if(l&&(o[l]||(o[l]={message:u.message,type:u.type}),v)){var c=o[l].types,f=c&&c[u.type];o[l]=r.appendErrors(l,v,o,u.type,f?[].concat(f,u.message):u.message)}t.issues.shift()}return{values:{},errors:e.toNestErrors(o,n)}}return{values:a.raw?i:t.output,errors:{}}})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=valibot.js.map
