// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`effectTsResolver > should return a single error from effectTsResolver when validation fails 1`] = `
{
  "errors": {
    "animal": {
      "message": "Expected "snake", actual ["dog"]",
      "ref": undefined,
      "type": "Type",
    },
    "luckyNumbers": [
      ,
      ,
      {
        "message": "Expected number, actual "3"",
        "ref": undefined,
        "type": "Type",
      },
    ],
    "password": {
      "message": "At least 1 special character.",
      "ref": {
        "name": "password",
      },
      "type": "Refinement",
    },
    "vehicles": [
      ,
      {
        "horsepower": {
          "message": "is missing",
          "ref": undefined,
          "type": "Missing",
        },
      },
    ],
  },
  "values": {},
}
`;
